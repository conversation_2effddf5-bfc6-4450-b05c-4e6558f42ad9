import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeService extends ChangeNotifier {
  static const String _themeKey = 'isDarkMode';
  bool _isDarkMode = false;

  bool get isDarkMode => _isDarkMode;

  ThemeService() {
    _loadTheme();
  }

  /// تحميل إعدادات الثيم المحفوظة
  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isDarkMode = prefs.getBool(_themeKey) ?? false;
      notifyListeners();
    } catch (e) {
      print('خطأ في تحميل إعدادات الثيم: $e');
    }
  }

  /// تبديل الوضع المظلم/الفاتح
  Future<void> toggleTheme() async {
    try {
      _isDarkMode = !_isDarkMode;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_themeKey, _isDarkMode);
      notifyListeners();
    } catch (e) {
      print('خطأ في حفظ إعدادات الثيم: $e');
    }
  }

  /// الحصول على ثيم فاتح
  ThemeData get lightTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primarySwatch: Colors.green,
    primaryColor: Colors.green[700],
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.green[700],
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      color: Colors.white,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    ),
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        fontFamily: 'QaloonFont',
        color: Colors.black87,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        fontFamily: 'QaloonFont',
        color: Colors.black87,
      ),
      bodyLarge: TextStyle(
        fontFamily: 'QaloonFont',
        color: Colors.black87,
      ),
      bodyMedium: TextStyle(
        fontFamily: 'QaloonFont',
        color: Colors.black54,
      ),
    ),
  );

  /// الحصول على ثيم مظلم
  ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primarySwatch: Colors.green,
    primaryColor: Colors.green[400],
    scaffoldBackgroundColor: const Color(0xFF121212),
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.green[400],
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      color: const Color(0xFF1E1E1E),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    ),
    textTheme: const TextTheme(
      headlineLarge: TextStyle(
        fontFamily: 'QaloonFont',
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
      headlineMedium: TextStyle(
        fontFamily: 'QaloonFont',
        color: Colors.white,
      ),
      bodyLarge: TextStyle(
        fontFamily: 'QaloonFont',
        color: Colors.white,
      ),
      bodyMedium: TextStyle(
        fontFamily: 'QaloonFont',
        color: Colors.white70,
      ),
    ),
  );

  /// الحصول على الثيم الحالي
  ThemeData get currentTheme => _isDarkMode ? darkTheme : lightTheme;

  /// ألوان مخصصة للوضع الحالي
  Color get primaryColor => _isDarkMode ? Colors.green[400]! : Colors.green[700]!;
  Color get backgroundColor => _isDarkMode ? const Color(0xFF121212) : Colors.white;
  Color get cardColor => _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;
  Color get textColor => _isDarkMode ? Colors.white : Colors.black87;
  Color get subtitleColor => _isDarkMode ? Colors.white70 : Colors.black54;

  /// ألوان التدرج للخلفية
  List<Color> get backgroundGradient => _isDarkMode 
    ? [
        const Color(0xFF1A1A2E),
        const Color(0xFF16213E),
        const Color(0xFF0F3460),
      ]
    : [
        const Color(0xFF87CEEB), // Sky blue
        const Color(0xFFE0F6FF), // Light blue
        const Color(0xFFFFF8DC), // Cornsilk
      ];
}
