import 'package:json_annotation/json_annotation.dart';

part 'surah.g.dart';

@JsonSerializable()
class Surah {
  final int id;
  final String name;
  @Json<PERSON>ey(name: 'ayah_count')
  final int ayahCount;
  @Json<PERSON>ey(name: 'order_number')
  final int orderNumber;
  final int juzNumber;
  final String audioFileName;

  const Surah({
    required this.id,
    required this.name,
    required this.ayahCount,
    required this.orderNumber,
    required this.juzNumber,
    required this.audioFileName,
  });

  factory Surah.fromJson(Map<String, dynamic> json) => _$SurahFromJson(json);
  Map<String, dynamic> toJson() => _$SurahToJson(this);

  String get audioPath {
    String juzFolder;
    switch (juzNumber) {
      case 26:
        juzFolder = 'juz_26';
        break;
      case 27:
        juzFolder = 'juz_27';
        break;
      case 28:
        juzFolder = 'juz_28';
        break;
      case 29:
        juzFolder = 'juz_29';
        break;
      case 30:
        juzFolder = 'juz_30';
        break;
      default:
        juzFolder = 'juz_30';
    }
    return 'assets/audio/$juzFolder/$audioFileName';
  }

  String get juzName {
    switch (juzNumber) {
      case 26:
        return 'الأحقاف';
      case 27:
        return 'الذاريات';
      case 28:
        return 'قد سمع';
      case 29:
        return 'تبارك';
      case 30:
        return 'عمّ';
      default:
        return 'عمّ';
    }
  }
}
