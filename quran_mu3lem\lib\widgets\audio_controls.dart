import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import '../services/audio_service.dart';

class AudioControls extends StatefulWidget {
  final AudioService audioService;
  final Color primaryColor;
  final bool isDarkMode;

  const AudioControls({
    super.key,
    required this.audioService,
    required this.primaryColor,
    required this.isDarkMode,
  });

  @override
  State<AudioControls> createState() => _AudioControlsState();
}

class _AudioControlsState extends State<AudioControls> {
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  bool _isPlaying = false;
  bool _isRepeatMode = false;

  @override
  void initState() {
    super.initState();
    _setupAudioListeners();
  }

  void _setupAudioListeners() {
    // Listen to player state changes
    widget.audioService.playerStateStream.listen((playerState) {
      if (mounted) {
        setState(() {
          _isPlaying = playerState.playing;
        });
      }
    });

    // Listen to position changes
    widget.audioService.positionStream.listen((position) {
      if (mounted) {
        setState(() {
          _currentPosition = position;
        });
      }
    });

    // Listen to duration changes
    widget.audioService.durationStream.listen((duration) {
      if (mounted) {
        setState(() {
          _totalDuration = duration ?? Duration.zero;
        });
      }
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? const Color(0xFF2D2D2D) : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress bar
          Row(
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: TextStyle(
                  fontSize: 12,
                  color: widget.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
              Expanded(
                child: Slider(
                  value: _totalDuration.inMilliseconds > 0
                      ? _currentPosition.inMilliseconds / _totalDuration.inMilliseconds
                      : 0.0,
                  onChanged: (value) {
                    final position = Duration(
                      milliseconds: (value * _totalDuration.inMilliseconds).round(),
                    );
                    widget.audioService.seek(position);
                  },
                  activeColor: widget.primaryColor,
                  inactiveColor: widget.primaryColor.withOpacity(0.3),
                ),
              ),
              Text(
                _formatDuration(_totalDuration),
                style: TextStyle(
                  fontSize: 12,
                  color: widget.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Control buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Repeat button
              IconButton(
                onPressed: () {
                  widget.audioService.toggleRepeatMode();
                  setState(() {
                    _isRepeatMode = widget.audioService.isRepeatMode;
                  });
                },
                icon: Icon(
                  Icons.repeat,
                  color: _isRepeatMode
                      ? widget.primaryColor
                      : (widget.isDarkMode ? Colors.grey[400] : Colors.grey[600]),
                  size: 28,
                ),
              ),

              // Previous button (placeholder)
              IconButton(
                onPressed: () {
                  // TODO: Implement previous surah functionality
                },
                icon: Icon(
                  Icons.skip_previous_rounded,
                  color: widget.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  size: 32,
                ),
              ),

              // Play/Pause button
              Container(
                decoration: BoxDecoration(
                  color: widget.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: () async {
                    if (_isPlaying) {
                      await widget.audioService.pause();
                    } else {
                      await widget.audioService.play();
                    }
                  },
                  icon: Icon(
                    _isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                    color: Colors.white,
                    size: 36,
                  ),
                ),
              ),

              // Next button (placeholder)
              IconButton(
                onPressed: () {
                  // TODO: Implement next surah functionality
                },
                icon: Icon(
                  Icons.skip_next_rounded,
                  color: widget.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  size: 32,
                ),
              ),

              // Stop button
              IconButton(
                onPressed: () async {
                  await widget.audioService.stop();
                },
                icon: Icon(
                  Icons.stop_rounded,
                  color: widget.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  size: 28,
                ),
              ),
            ],
          ),

          const SizedBox(height: 10),

          // Surah info
          if (widget.audioService.currentSurah != null)
            Text(
              widget.audioService.currentSurah!.name,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.isDarkMode ? Colors.white : Colors.black87,
                fontFamily: 'QaloonFont',
              ),
            ),
        ],
      ),
    );
  }
}
