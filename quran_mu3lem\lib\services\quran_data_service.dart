import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/surah.dart';
import '../models/juz.dart';
import '../models/ayah.dart';

class QuranDataService {
  static final QuranDataService _instance = QuranDataService._internal();
  factory QuranDataService() => _instance;
  QuranDataService._internal();

  List<Surah>? _allSurahs;
  List<Juz>? _juzList;
  Map<int, List<Ayah>>? _ayahsBySurah;

  // تحديد السور لكل جزء
  static const Map<int, List<int>> _juzSurahs = {
    28: [46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66], // جزء قد سمع
    29: [67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77], // جزء تبارك
    30: [1, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114], // جزء عم + الفاتحة
  };

  // أسماء الملفات الصوتية
  static const Map<int, String> _audioFileNames = {
    1: 'الفاتحة.mp3',
    46: 'الأحقاف.mp3',
    47: 'محمد.mp3',
    48: 'الفتح.mp3',
    49: 'الحجرات.mp3',
    50: 'ق.mp3',
    51: 'الذاريات.mp3',
    52: 'الطور.mp3',
    53: 'النجم.mp3',
    54: 'القمر.mp3',
    55: 'الرحمن.mp3',
    56: 'الواقعة.mp3',
    57: 'الحديد.mp3',
    58: 'المجادلة.mp3',
    59: 'الحشر.mp3',
    60: 'الممتحنة.mp3',
    61: 'الصف.mp3',
    62: 'الجمعة.mp3',
    63: 'المنافقون.mp3',
    64: 'التغابن.mp3',
    65: 'الطلاق.mp3',
    66: 'التحريم.mp3',
    67: 'الملك.mp3',
    68: 'القلم.mp3',
    69: 'الحاقة.mp3',
    70: 'المعارج.mp3',
    71: 'نوح.mp3',
    72: 'الجن.mp3',
    73: 'المزمل.mp3',
    74: 'المدثر.mp3',
    75: 'القيامة.mp3',
    76: 'الانسان.mp3',
    77: 'المرسلات.mp3',
    78: 'النبأ.mp3',
    79: 'النازعات.mp3',
    80: 'عبس.mp3',
    81: 'التكوير.mp3',
    82: 'الانفطار.mp3',
    83: 'المطففين.mp3',
    84: 'الانشقاق.mp3',
    85: 'البروج.mp3',
    86: 'الطارق.mp3',
    87: 'الاعلى.mp3',
    88: 'الغاشية.mp3',
    89: 'الفجر.mp3',
    90: 'البلد.mp3',
    91: 'الشمس.mp3',
    92: 'الليل.mp3',
    93: 'الضحى.mp3',
    94: 'الشرح.mp3',
    95: 'التين.mp3',
    96: 'العلق.mp3',
    97: 'القدر.mp3',
    98: 'البينة.mp3',
    99: 'الزلزلة.mp3',
    100: 'العاديات.mp3',
    101: 'القارعة.mp3',
    102: 'التكاثر.mp3',
    103: 'العصر.mp3',
    104: 'الهمزة.mp3',
    105: 'الفيل.mp3',
    106: 'قريش.mp3',
    107: 'الماعون.mp3',
    108: 'الكوثر.mp3',
    109: 'الكافرون.mp3',
    110: 'النصر.mp3',
    111: 'المسد.mp3',
    112: 'الخلاص.mp3',
    113: 'الفلق.mp3',
    114: 'الناس.mp3',
  };

  Future<List<Surah>> loadAllSurahs() async {
    if (_allSurahs != null) return _allSurahs!;

    try {
      final String jsonString = await rootBundle.loadString('assets/data/surahs_index.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> surahsJson = jsonData['surahs_index'];

      _allSurahs = surahsJson.map((json) {
        final surahId = json['id'] as int;
        final juzNumber = _getJuzNumber(surahId);
        final audioFileName = _audioFileNames[surahId] ?? '';

        return Surah(
          id: surahId,
          name: json['name'],
          ayahCount: json['ayah_count'],
          orderNumber: json['order_number'],
          juzNumber: juzNumber,
          audioFileName: audioFileName,
        );
      }).toList();

      return _allSurahs!;
    } catch (e) {
      throw Exception('خطأ في تحميل بيانات السور: $e');
    }
  }

  Future<List<Juz>> loadJuzList() async {
    if (_juzList != null) return _juzList!;

    final allSurahs = await loadAllSurahs();
    
    _juzList = [
      Juz(
        number: 28,
        name: 'قد سمع',
        description: 'الجزء الثامن والعشرون',
        surahs: allSurahs.where((s) => s.juzNumber == 28).toList(),
      ),
      Juz(
        number: 29,
        name: 'تبارك',
        description: 'الجزء التاسع والعشرون',
        surahs: allSurahs.where((s) => s.juzNumber == 29).toList(),
      ),
      Juz(
        number: 30,
        name: 'عمّ',
        description: 'الجزء الثلاثون',
        surahs: allSurahs.where((s) => s.juzNumber == 30).toList(),
      ),
    ];

    return _juzList!;
  }

  Future<List<Surah>> getSurahsByJuz(int juzNumber) async {
    final allSurahs = await loadAllSurahs();
    return allSurahs.where((surah) => surah.juzNumber == juzNumber).toList();
  }

  Future<Surah?> getSurahById(int id) async {
    final allSurahs = await loadAllSurahs();
    try {
      return allSurahs.firstWhere((surah) => surah.id == id);
    } catch (e) {
      return null;
    }
  }

  int _getJuzNumber(int surahId) {
    for (final entry in _juzSurahs.entries) {
      if (entry.value.contains(surahId)) {
        return entry.key;
      }
    }
    return 30; // افتراضي
  }

  // دالة لتحميل نص السورة (سيتم تطويرها لاحقاً)
  Future<List<Ayah>> getAyahsBySurah(int surahId) async {
    // هذه دالة مؤقتة - سيتم تطويرها لاحقاً لتحميل النص من ملف docx
    return [];
  }
}
