import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/surah.dart';
import '../models/juz.dart';
import '../models/ayah.dart';

class QuranDataService {
  static final QuranDataService _instance = QuranDataService._internal();
  factory QuranDataService() => _instance;
  QuranDataService._internal();

  List<Surah>? _allSurahs;
  List<Juz>? _juzList;
  Map<int, List<Ayah>>? _ayahsBySurah;

  // تحديد السور لكل جزء حسب ترتيب المصحف الشريف (من الناس إلى الأحقاف)
  static const Map<int, List<int>> _juzSurahs = {
    // الجزء 30 - عمّ (من سورة الناس إلى سورة النبأ + الفاتحة)
    30: [114, 113, 112, 111, 110, 109, 108, 107, 106, 105, 104, 103, 102, 101, 100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 90, 89, 88, 87, 86, 85, 84, 83, 82, 81, 80, 79, 78, 1], // عمّ + الفاتحة

    // الجزء 29 - تبارك (من سورة المرسلات إلى سورة الملك)
    29: [77, 76, 75, 74, 73, 72, 71, 70, 69, 68, 67], // تبارك

    // الجزء 28 - قد سمع (من سورة التحريم إلى سورة الأحقاف)
    28: [66, 65, 64, 63, 62, 61, 60, 59, 58, 57, 56, 55, 54, 53, 52, 51, 50, 49, 48, 47, 46], // قد سمع
  };

  // أسماء الملفات الصوتية - مرتبة حسب ترتيب المصحف الشريف
  static const Map<int, String> _audioFileNames = {
    // الفاتحة
    1: 'الفاتحة.mp3',

    // الجزء 28 - قد سمع (من الأحقاف إلى التحريم)
    46: 'الأحقاف.mp3',
    47: 'محمد.mp3',
    48: 'الفتح.mp3',
    49: 'الحجرات.mp3',
    50: 'ق.mp3',
    51: 'الذاريات.mp3',
    52: 'الطور.mp3',
    53: 'النجم.mp3',
    54: 'القمر.mp3',
    55: 'الرحمن.mp3',
    56: 'الواقعة.mp3',
    57: 'الحديد.mp3',
    58: 'المجادلة.mp3',
    59: 'الحشر.mp3',
    60: 'الممتحنة.mp3',
    61: 'الصف.mp3',
    62: 'الجمعة.mp3',
    63: 'المنافقون.mp3',
    64: 'التغابن.mp3',
    65: 'الطلاق.mp3',
    66: 'التحريم.mp3',

    // الجزء 29 - تبارك (من الملك إلى المرسلات)
    67: 'الملك.mp3',
    68: 'القلم.mp3',
    69: 'الحاقة.mp3',
    70: 'المعارج.mp3',
    71: 'نوح.mp3',
    72: 'الجن.mp3',
    73: 'المزمل.mp3',
    74: 'المدثر.mp3',
    75: 'القيامة.mp3',
    76: 'الانسان.mp3',
    77: 'المرسلات.mp3',

    // الجزء 30 - عمّ (من النبأ إلى الناس)
    78: 'النبأ.mp3',
    79: 'النازعات.mp3',
    80: 'عبس.mp3',
    81: 'التكوير.mp3',
    82: 'الانفطار.mp3',
    83: 'المطففين.mp3',
    84: 'الانشقاق.mp3',
    85: 'البروج.mp3',
    86: 'الطارق.mp3',
    87: 'الأعلى.mp3',
    88: 'الغاشية.mp3',
    89: 'الفجر.mp3',
    90: 'البلد.mp3',
    91: 'الشمس.mp3',
    92: 'الليل.mp3',
    93: 'الضحى.mp3',
    94: 'الشرح.mp3',
    95: 'التين.mp3',
    96: 'العلق.mp3',
    97: 'القدر.mp3',
    98: 'البينة.mp3',
    99: 'الزلزلة.mp3',
    100: 'العاديات.mp3',
    101: 'القارعة.mp3',
    102: 'التكاثر.mp3',
    103: 'العصر.mp3',
    104: 'الهمزة.mp3',
    105: 'الفيل.mp3',
    106: 'قريش.mp3',
    107: 'الماعون.mp3',
    108: 'الكوثر.mp3',
    109: 'الكافرون.mp3',
    110: 'النصر.mp3',
    111: 'المسد.mp3',
    112: 'الإخلاص.mp3',
    113: 'الفلق.mp3',
    114: 'الناس.mp3',
  };

  Future<List<Surah>> loadAllSurahs() async {
    if (_allSurahs != null) return _allSurahs!;

    try {
      final String jsonString = await rootBundle.loadString('assets/data/surahs_index.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final List<dynamic> surahsJson = jsonData['surahs_index'];

      _allSurahs = surahsJson.map((json) {
        final surahId = json['id'] as int;
        final juzNumber = _getJuzNumber(surahId);
        final audioFileName = _audioFileNames[surahId] ?? '';

        return Surah(
          id: surahId,
          name: json['name'],
          ayahCount: json['ayah_count'],
          orderNumber: json['order_number'],
          juzNumber: juzNumber,
          audioFileName: audioFileName,
        );
      }).toList();

      return _allSurahs!;
    } catch (e) {
      throw Exception('خطأ في تحميل بيانات السور: $e');
    }
  }

  Future<List<Juz>> loadJuzList() async {
    if (_juzList != null) return _juzList!;

    final allSurahs = await loadAllSurahs();
    
    _juzList = [
      Juz(
        number: 30,
        name: 'عمّ',
        description: 'الجزء الثلاثون',
        surahs: allSurahs.where((s) => s.juzNumber == 30).toList(),
      ),
      Juz(
        number: 29,
        name: 'تبارك',
        description: 'الجزء التاسع والعشرون',
        surahs: allSurahs.where((s) => s.juzNumber == 29).toList(),
      ),
      Juz(
        number: 28,
        name: 'قد سمع',
        description: 'الجزء الثامن والعشرون',
        surahs: allSurahs.where((s) => s.juzNumber == 28).toList(),
      ),
    ];

    return _juzList!;
  }

  Future<List<Surah>> getSurahsByJuz(int juzNumber) async {
    final allSurahs = await loadAllSurahs();
    return allSurahs.where((surah) => surah.juzNumber == juzNumber).toList();
  }

  Future<Surah?> getSurahById(int id) async {
    final allSurahs = await loadAllSurahs();
    try {
      return allSurahs.firstWhere((surah) => surah.id == id);
    } catch (e) {
      return null;
    }
  }

  int _getJuzNumber(int surahId) {
    for (final entry in _juzSurahs.entries) {
      if (entry.value.contains(surahId)) {
        return entry.key;
      }
    }
    return 30; // افتراضي
  }

  // دالة لتحميل نص السورة (سيتم تطويرها لاحقاً)
  Future<List<Ayah>> getAyahsBySurah(int surahId) async {
    // هذه دالة مؤقتة - سيتم تطويرها لاحقاً لتحميل النص من ملف docx
    return [];
  }
}
