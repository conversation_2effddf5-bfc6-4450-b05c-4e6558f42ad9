{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Project\\QuranMu3lem\\quran_mu3lem\\android\\app\\.cxx\\Debug\\m4c2s4c5\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Project\\QuranMu3lem\\quran_mu3lem\\android\\app\\.cxx\\Debug\\m4c2s4c5\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}