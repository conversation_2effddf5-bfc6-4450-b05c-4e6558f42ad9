import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../services/audio_service.dart';
import '../services/quran_data_service.dart';

class AudioTest {
  static final AudioService _audioService = AudioService();
  static final QuranDataService _dataService = QuranDataService();

  /// اختبار تحميل البيانات
  static Future<void> testDataLoading() async {
    try {
      if (kDebugMode) {
        print('🧪 اختبار تحميل البيانات...');
      }
      
      final surahs = await _dataService.loadAllSurahs();
      if (kDebugMode) {
        print('✅ تم تحميل ${surahs.length} سورة');
      }
      
      final juz30Surahs = await _dataService.getSurahsByJuz(30);
      if (kDebugMode) {
        print('✅ جزء عمّ يحتوي على ${juz30Surahs.length} سورة');
        
        for (final surah in juz30Surahs.take(5)) {
          print('   - ${surah.name} (${surah.audioFileName})');
        }
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل البيانات: $e');
      }
    }
  }

  /// اختبار توفر الملفات الصوتية
  static Future<void> testAudioFiles() async {
    try {
      if (kDebugMode) {
        print('🧪 اختبار توفر الملفات الصوتية...');
      }
      
      final availableFiles = _audioService.availableAudioFiles;
      if (kDebugMode) {
        print('✅ الملفات الصوتية المتاحة: ${availableFiles.length}');
        for (final file in availableFiles) {
          print('   - $file');
        }
      }
      
      // اختبار وجود الملفات فعلياً
      for (final fileName in availableFiles) {
        try {
          final path = 'audio/juz_30/$fileName';
          await rootBundle.load(path);
          if (kDebugMode) {
            print('✅ الملف موجود: $fileName');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ الملف غير موجود: $fileName');
          }
        }
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في اختبار الملفات الصوتية: $e');
      }
    }
  }

  /// اختبار تحميل سورة معينة
  static Future<void> testLoadSurah(int surahId) async {
    try {
      if (kDebugMode) {
        print('🧪 اختبار تحميل السورة رقم $surahId...');
      }
      
      final surah = await _dataService.getSurahById(surahId);
      if (surah == null) {
        if (kDebugMode) {
          print('❌ السورة غير موجودة');
        }
        return;
      }
      
      if (kDebugMode) {
        print('📖 السورة: ${surah.name}');
        print('📁 الملف: ${surah.audioFileName}');
        print('📍 المسار: ${surah.audioPath}');
      }
      
      await _audioService.playSurah(surah);
      if (kDebugMode) {
        print('✅ تم تحميل السورة بنجاح');
        print('🎵 حالة المشغل: ${_audioService.playerStatus}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل السورة: $e');
      }
    }
  }

  /// تشغيل جميع الاختبارات
  static Future<void> runAllTests() async {
    if (kDebugMode) {
      print('🚀 بدء تشغيل اختبارات الصوت...');
      print('=' * 50);
    }
    
    await testDataLoading();
    print('');
    
    await testAudioFiles();
    print('');
    
    // اختبار تحميل سورة الفاتحة
    await testLoadSurah(1);
    print('');
    
    // اختبار تحميل سورة النبأ
    await testLoadSurah(78);
    print('');
    
    if (kDebugMode) {
      print('=' * 50);
      print('🏁 انتهت الاختبارات');
    }
  }
}
