import 'package:just_audio/just_audio.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import '../models/surah.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  Surah? _currentSurah;
  bool _isRepeatMode = false;

  AudioPlayer get audioPlayer => _audioPlayer;
  Surah? get currentSurah => _currentSurah;
  bool get isRepeatMode => _isRepeatMode;

  Stream<PlayerState> get playerStateStream => _audioPlayer.playerStateStream;
  Stream<Duration> get positionStream => _audioPlayer.positionStream;
  Stream<Duration?> get durationStream => _audioPlayer.durationStream;

  Future<void> playSurah(Surah surah) async {
    try {
      _currentSurah = surah;

      // تحقق من وجود الملف الصوتي
      print('محاولة تحميل الملف الصوتي: ${surah.audioPath}');
      print('اسم السورة: ${surah.name}');
      print('رقم الجزء: ${surah.juzNumber}');

      // إيقاف أي تشغيل سابق
      await _audioPlayer.stop();

      // استخدام setAsset للملفات في مجلد assets
      await _audioPlayer.setAsset(surah.audioPath);

      if (_isRepeatMode) {
        await _audioPlayer.setLoopMode(LoopMode.one);
      } else {
        await _audioPlayer.setLoopMode(LoopMode.off);
      }

      print('تم تحميل الملف الصوتي بنجاح');
      // لا نبدأ التشغيل تلقائياً، ننتظر أن يضغط المستخدم على زر التشغيل
    } catch (e) {
      print('خطأ في تحميل الصوت: $e');
      _currentSurah = null; // إعادة تعيين السورة الحالية

      // معالجة أنواع مختلفة من الأخطاء
      String errorMessage = 'خطأ غير معروف في تحميل الصوت';

      if (e.toString().contains('Unable to load asset') ||
          e.toString().contains('FileSystemException') ||
          e.toString().contains('404') ||
          e.toString().contains('Not found')) {
        errorMessage = 'الملف الصوتي غير موجود: ${surah.audioPath}';
      } else if (e.toString().contains('Format not supported') ||
                 e.toString().contains('Codec')) {
        errorMessage = 'تنسيق الملف الصوتي غير مدعوم';
      } else if (e.toString().contains('Network') ||
                 e.toString().contains('Connection')) {
        errorMessage = 'مشكلة في الاتصال أثناء تحميل الصوت';
      } else {
        errorMessage = 'خطأ في تحميل الصوت: ${e.toString()}';
      }

      throw Exception(errorMessage);
    }
  }

  Future<void> play() async {
    await _audioPlayer.play();
  }

  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  Future<void> stop() async {
    await _audioPlayer.stop();
    _currentSurah = null;
  }

  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  void toggleRepeatMode() {
    _isRepeatMode = !_isRepeatMode;
    if (_isRepeatMode) {
      _audioPlayer.setLoopMode(LoopMode.one);
    } else {
      _audioPlayer.setLoopMode(LoopMode.off);
    }
  }

  bool get isPlaying => _audioPlayer.playing;
  bool get isPaused => !_audioPlayer.playing && _audioPlayer.processingState != ProcessingState.idle;

  Duration get currentPosition => _audioPlayer.position;
  Duration? get totalDuration => _audioPlayer.duration;

  void dispose() {
    _audioPlayer.dispose();
  }
}
