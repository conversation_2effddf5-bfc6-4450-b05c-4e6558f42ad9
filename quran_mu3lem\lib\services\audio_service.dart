import 'package:just_audio/just_audio.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import '../models/surah.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  Surah? _currentSurah;
  bool _isRepeatMode = false;
  bool _isLoading = false;

  AudioPlayer get audioPlayer => _audioPlayer;
  Surah? get currentSurah => _currentSurah;
  bool get isRepeatMode => _isRepeatMode;
  bool get isLoading => _isLoading;

  Stream<PlayerState> get playerStateStream => _audioPlayer.playerStateStream;
  Stream<Duration> get positionStream => _audioPlayer.positionStream;
  Stream<Duration?> get durationStream => _audioPlayer.durationStream;

  // قائمة بالملفات الصوتية المتاحة فعلياً
  static const List<String> _availableAudioFiles = [
    'الفاتحة.mp3',
    'النبأ.mp3',
    'النازعات.mp3',
    'عبس.mp3',
    'الملك.mp3',
  ];

  // دالة للتحقق من توفر الملف الصوتي
  bool _isAudioFileAvailable(String fileName) {
    return _availableAudioFiles.contains(fileName);
  }

  Future<void> playSurah(Surah surah) async {
    if (_isLoading) {
      throw Exception('جاري تحميل ملف صوتي آخر، يرجى الانتظار');
    }

    _isLoading = true;

    try {
      // التحقق من توفر الملف الصوتي أولاً
      if (!_isAudioFileAvailable(surah.audioFileName)) {
        throw Exception('الملف الصوتي "${surah.audioFileName}" غير متوفر حالياً');
      }

      _currentSurah = surah;

      // طباعة معلومات التشخيص
      if (kDebugMode) {
        print('=== معلومات تحميل الصوت ===');
        print('اسم السورة: ${surah.name}');
        print('رقم الجزء: ${surah.juzNumber}');
        print('اسم الملف: ${surah.audioFileName}');
        print('المسار الكامل: ${surah.audioPath}');
        print('========================');
      }

      // إيقاف أي تشغيل سابق
      await _audioPlayer.stop();

      // محاولة تحميل الملف الصوتي
      await _loadAudioFile(surah.audioPath);

      // إعداد وضع التكرار
      if (_isRepeatMode) {
        await _audioPlayer.setLoopMode(LoopMode.one);
      } else {
        await _audioPlayer.setLoopMode(LoopMode.off);
      }

      if (kDebugMode) {
        print('✅ تم تحميل الملف الصوتي بنجاح');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل الصوت: $e');
      }

      _currentSurah = null;
      _handleAudioError(e, surah);
    } finally {
      _isLoading = false;
    }
  }

  Future<void> _loadAudioFile(String audioPath) async {
    try {
      // محاولة تحميل الملف من الأصول
      await _audioPlayer.setAsset(audioPath);
    } catch (e) {
      // إذا فشل التحميل، نجرب مسارات بديلة
      if (kDebugMode) {
        print('فشل تحميل الملف من: $audioPath');
        print('محاولة مسارات بديلة...');
      }

      // محاولة إضافة assets/ في البداية إذا لم تكن موجودة
      String alternativePath = audioPath.startsWith('assets/')
          ? audioPath.substring(7) // إزالة assets/
          : 'assets/$audioPath'; // إضافة assets/

      try {
        await _audioPlayer.setAsset(alternativePath);
        if (kDebugMode) {
          print('✅ نجح التحميل من المسار البديل: $alternativePath');
        }
      } catch (e2) {
        if (kDebugMode) {
          print('❌ فشل التحميل من المسار البديل أيضاً: $alternativePath');
        }
        rethrow;
      }
    }
  }

  void _handleAudioError(dynamic error, Surah surah) {
    String errorMessage = 'خطأ غير معروف في تحميل الصوت';

    String errorString = error.toString().toLowerCase();

    if (errorString.contains('unable to load asset') ||
        errorString.contains('file not found') ||
        errorString.contains('404') ||
        errorString.contains('not found')) {
      errorMessage = 'الملف الصوتي غير موجود:\n${surah.audioFileName}\n\nتأكد من وجود الملف في مجلد الأصول';
    } else if (errorString.contains('format not supported') ||
               errorString.contains('codec') ||
               errorString.contains('unsupported')) {
      errorMessage = 'تنسيق الملف الصوتي غير مدعوم\nيرجى استخدام ملف MP3 صالح';
    } else if (errorString.contains('network') ||
               errorString.contains('connection') ||
               errorString.contains('timeout')) {
      errorMessage = 'مشكلة في الاتصال أثناء تحميل الصوت';
    } else if (errorString.contains('permission')) {
      errorMessage = 'ليس لديك صلاحية للوصول إلى الملف الصوتي';
    } else {
      errorMessage = 'خطأ في تحميل الصوت:\n${error.toString()}';
    }

    throw Exception(errorMessage);
  }

  Future<void> play() async {
    if (_currentSurah == null) {
      throw Exception('لم يتم تحميل أي سورة للتشغيل');
    }

    if (_audioPlayer.processingState == ProcessingState.idle) {
      throw Exception('يجب تحميل الملف الصوتي أولاً');
    }

    await _audioPlayer.play();
  }

  Future<void> pause() async {
    if (_audioPlayer.playing) {
      await _audioPlayer.pause();
    }
  }

  Future<void> stop() async {
    await _audioPlayer.stop();
    _currentSurah = null;
  }

  Future<void> seek(Duration position) async {
    if (_audioPlayer.duration != null && position <= _audioPlayer.duration!) {
      await _audioPlayer.seek(position);
    }
  }

  void toggleRepeatMode() {
    _isRepeatMode = !_isRepeatMode;
    if (_isRepeatMode) {
      _audioPlayer.setLoopMode(LoopMode.one);
    } else {
      _audioPlayer.setLoopMode(LoopMode.off);
    }
  }

  bool get isPlaying => _audioPlayer.playing;
  bool get isPaused => !_audioPlayer.playing && _audioPlayer.processingState != ProcessingState.idle;
  bool get isReady => _audioPlayer.processingState == ProcessingState.ready;

  Duration get currentPosition => _audioPlayer.position;
  Duration? get totalDuration => _audioPlayer.duration;

  // دالة للحصول على قائمة السور المتاحة للتشغيل
  List<String> get availableAudioFiles => List.unmodifiable(_availableAudioFiles);

  // دالة للتحقق من حالة المشغل
  String get playerStatus {
    switch (_audioPlayer.processingState) {
      case ProcessingState.idle:
        return 'غير نشط';
      case ProcessingState.loading:
        return 'جاري التحميل...';
      case ProcessingState.buffering:
        return 'جاري التخزين المؤقت...';
      case ProcessingState.ready:
        return _audioPlayer.playing ? 'يتم التشغيل' : 'جاهز للتشغيل';
      case ProcessingState.completed:
        return 'انتهى التشغيل';
    }
  }

  void dispose() {
    _audioPlayer.dispose();
  }
}
