import 'package:just_audio/just_audio.dart';
import '../models/surah.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  Surah? _currentSurah;
  bool _isRepeatMode = false;

  AudioPlayer get audioPlayer => _audioPlayer;
  Surah? get currentSurah => _currentSurah;
  bool get isRepeatMode => _isRepeatMode;

  Stream<PlayerState> get playerStateStream => _audioPlayer.playerStateStream;
  Stream<Duration> get positionStream => _audioPlayer.positionStream;
  Stream<Duration?> get durationStream => _audioPlayer.durationStream;

  Future<void> playSurah(Surah surah) async {
    try {
      _currentSurah = surah;
      await _audioPlayer.setAsset(surah.audioPath);
      
      if (_isRepeatMode) {
        await _audioPlayer.setLoopMode(LoopMode.one);
      } else {
        await _audioPlayer.setLoopMode(LoopMode.off);
      }
      
      await _audioPlayer.play();
    } catch (e) {
      throw Exception('خطأ في تشغيل الصوت: $e');
    }
  }

  Future<void> play() async {
    await _audioPlayer.play();
  }

  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  Future<void> stop() async {
    await _audioPlayer.stop();
    _currentSurah = null;
  }

  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  void toggleRepeatMode() {
    _isRepeatMode = !_isRepeatMode;
    if (_isRepeatMode) {
      _audioPlayer.setLoopMode(LoopMode.one);
    } else {
      _audioPlayer.setLoopMode(LoopMode.off);
    }
  }

  bool get isPlaying => _audioPlayer.playing;
  bool get isPaused => !_audioPlayer.playing && _audioPlayer.processingState != ProcessingState.idle;

  Duration get currentPosition => _audioPlayer.position;
  Duration? get totalDuration => _audioPlayer.duration;

  void dispose() {
    _audioPlayer.dispose();
  }
}
