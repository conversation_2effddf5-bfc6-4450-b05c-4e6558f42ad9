import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'surah_list_screen.dart';
import 'about_screen.dart';
import '../utils/audio_test.dart';
import '../services/audio_test_service.dart';
import '../services/theme_service.dart';
import '../services/about_audio_service.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text(
              'القرآن المعلم',
              style: TextStyle(
                fontFamily: 'QaloonFont',
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: themeService.primaryColor,
            foregroundColor: Colors.white,
            centerTitle: true,
            elevation: 0,
            actions: [
              // زر تبديل الوضع المظلم
              IconButton(
                onPressed: () async {
                  await themeService.toggleTheme();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          themeService.isDarkMode 
                            ? 'تم تفعيل الوضع المظلم' 
                            : 'تم تفعيل الوضع الفاتح',
                          style: const TextStyle(fontFamily: 'QaloonFont'),
                        ),
                        duration: const Duration(seconds: 1),
                        backgroundColor: themeService.primaryColor,
                      ),
                    );
                  }
                },
                icon: Icon(
                  themeService.isDarkMode 
                    ? Icons.light_mode 
                    : Icons.dark_mode,
                ),
                tooltip: themeService.isDarkMode 
                  ? 'الوضع الفاتح' 
                  : 'الوضع المظلم',
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: themeService.backgroundGradient,
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Header
                      const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: themeService.cardColor,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Text(
                            'القرآن المعلم',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: themeService.textColor,
                              fontFamily: 'QaloonFont',
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'الأجزاء الخمسة الأخيرة',
                            style: TextStyle(
                              fontSize: 16,
                              color: themeService.subtitleColor,
                              fontFamily: 'QaloonFont',
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 30),

                    // Juz buttons - الأجزاء الخمسة الأخيرة من 30 إلى 26
                    Column(
                      children: [
                        _buildJuzButton(
                          context,
                          themeService,
                          juzNumber: 30,
                          title: 'الجزء الثلاثون',
                          subtitle: 'عمّ',
                          color: const Color(0xFFFF9800),
                        ),
                        const SizedBox(height: 12),
                        _buildJuzButton(
                          context,
                          themeService,
                          juzNumber: 29,
                          title: 'الجزء التاسع والعشرون',
                          subtitle: 'تبارك',
                          color: const Color(0xFF2196F3),
                        ),
                        const SizedBox(height: 12),
                        _buildJuzButton(
                          context,
                          themeService,
                          juzNumber: 28,
                          title: 'الجزء الثامن والعشرون',
                          subtitle: 'قد سمع',
                          color: const Color(0xFF4CAF50),
                        ),
                        const SizedBox(height: 12),
                        _buildJuzButton(
                          context,
                          themeService,
                          juzNumber: 27,
                          title: 'الجزء السابع والعشرون',
                          subtitle: 'الذاريات',
                          color: const Color(0xFF9C27B0),
                        ),
                        const SizedBox(height: 12),
                        _buildJuzButton(
                          context,
                          themeService,
                          juzNumber: 26,
                          title: 'الجزء السادس والعشرون',
                          subtitle: 'الأحقاف',
                          color: const Color(0xFFE91E63),
                        ),
                      ],
                    ),

                    const SizedBox(height: 25),

                    // Footer
                    Text(
                      'برواية قالون عن نافع',
                      style: TextStyle(
                        fontSize: 16,
                        color: themeService.primaryColor,
                        fontFamily: 'QaloonFont',
                      ),
                    ),

                    const SizedBox(height: 15),

                    // زر عن التطبيق
                    SizedBox(
                      width: double.infinity,
                      height: 55,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AboutScreen(),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple[600],
                          foregroundColor: Colors.white,
                          elevation: 6,
                          shadowColor: Colors.purple.withOpacity(0.3),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.info_outline_rounded,
                                size: 24,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'عن التطبيق',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'QaloonFont',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Debug test buttons (only in debug mode)
                    if (kDebugMode) ...[
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('جاري تشغيل اختبارات الصوت...'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                                await AudioTest.runAllTests();
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('انتهت الاختبارات - تحقق من وحدة التحكم'),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                }
                              },
                              icon: const Icon(Icons.bug_report),
                              label: const Text('اختبار قديم'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('جاري فحص جميع الملفات الصوتية...'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                                
                                final audioTestService = AudioTestService();
                                await audioTestService.testAllAudioFiles();
                                
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('انتهى الفحص - تحقق من وحدة التحكم'),
                                      backgroundColor: Colors.blue,
                                    ),
                                  );
                                }
                              },
                              icon: const Icon(Icons.audio_file),
                              label: const Text('فحص الملفات'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],

                    const SizedBox(height: 30),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildJuzButton(
    BuildContext context,
    ThemeService themeService, {
    required int juzNumber,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 70,
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SurahListScreen(juzNumber: juzNumber),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          elevation: 6,
          shadowColor: color.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'QaloonFont',
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 13,
                        fontFamily: 'QaloonFont',
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios_rounded,
                size: 20,
                color: Colors.white,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
