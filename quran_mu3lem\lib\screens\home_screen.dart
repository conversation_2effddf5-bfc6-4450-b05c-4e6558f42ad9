import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'surah_list_screen.dart';
import '../utils/audio_test.dart';
import '../services/audio_test_service.dart';
import '../services/theme_service.dart';
import '../services/about_audio_service.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text(
              'القرآن المعلم',
              style: TextStyle(
                fontFamily: 'QaloonFont',
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: themeService.primaryColor,
            foregroundColor: Colors.white,
            centerTitle: true,
            elevation: 0,
            actions: [
              // زر تبديل الوضع المظلم
              IconButton(
                onPressed: () async {
                  await themeService.toggleTheme();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          themeService.isDarkMode 
                            ? 'تم تفعيل الوضع المظلم' 
                            : 'تم تفعيل الوضع الفاتح',
                          style: const TextStyle(fontFamily: 'QaloonFont'),
                        ),
                        duration: const Duration(seconds: 1),
                        backgroundColor: themeService.primaryColor,
                      ),
                    );
                  }
                },
                icon: Icon(
                  themeService.isDarkMode 
                    ? Icons.light_mode 
                    : Icons.dark_mode,
                ),
                tooltip: themeService.isDarkMode 
                  ? 'الوضع الفاتح' 
                  : 'الوضع المظلم',
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: themeService.backgroundGradient,
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    // Header
                    const SizedBox(height: 40),
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: themeService.cardColor,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.menu_book_rounded,
                            size: 60,
                            color: themeService.primaryColor,
                          ),
                          const SizedBox(height: 10),
                          Text(
                            'القرآن المعلم',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: themeService.textColor,
                              fontFamily: 'QaloonFont',
                            ),
                          ),
                          const SizedBox(height: 5),
                          Text(
                            'الأجزاء الثلاثة الأخيرة',
                            style: TextStyle(
                              fontSize: 18,
                              color: themeService.subtitleColor,
                              fontFamily: 'QaloonFont',
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 60),
                    
                    // Juz buttons
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildJuzButton(
                            context,
                            themeService,
                            juzNumber: 28,
                            title: 'الجزء الثامن والعشرون',
                            subtitle: 'قد سمع',
                            color: const Color(0xFF4CAF50),
                            icon: Icons.star_rounded,
                          ),
                          _buildJuzButton(
                            context,
                            themeService,
                            juzNumber: 29,
                            title: 'الجزء التاسع والعشرون',
                            subtitle: 'تبارك',
                            color: const Color(0xFF2196F3),
                            icon: Icons.auto_awesome_rounded,
                          ),
                          _buildJuzButton(
                            context,
                            themeService,
                            juzNumber: 30,
                            title: 'الجزء الثلاثون',
                            subtitle: 'عمّ',
                            color: const Color(0xFFFF9800),
                            icon: Icons.brightness_7_rounded,
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Footer
                    Text(
                      'برواية قالون عن نافع',
                      style: TextStyle(
                        fontSize: 16,
                        color: themeService.primaryColor,
                        fontFamily: 'QaloonFont',
                      ),
                    ),

                    const SizedBox(height: 20),

                    // زر عن التطبيق
                    Container(
                      width: double.infinity,
                      height: 80,
                      margin: const EdgeInsets.symmetric(vertical: 10),
                      child: ElevatedButton(
                        onPressed: () async {
                          final aboutAudioService = AboutAudioService();
                          
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Text(
                                'جاري تشغيل معلومات عن التطبيق...',
                                style: TextStyle(fontFamily: 'QaloonFont'),
                              ),
                              duration: const Duration(seconds: 2),
                              backgroundColor: themeService.primaryColor,
                            ),
                          );
                          
                          await aboutAudioService.playAboutAudio();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple[600],
                          foregroundColor: Colors.white,
                          elevation: 8,
                          shadowColor: Colors.purple.withOpacity(0.5),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.info_outline_rounded,
                                size: 30,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 15),
                            const Text(
                              'عن التطبيق',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'QaloonFont',
                              ),
                            ),
                            const SizedBox(width: 15),
                            const Icon(
                              Icons.play_circle_outline_rounded,
                              size: 30,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Debug test buttons (only in debug mode)
                    if (kDebugMode) ...[
                      const SizedBox(height: 20),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('جاري تشغيل اختبارات الصوت...'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                                await AudioTest.runAllTests();
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('انتهت الاختبارات - تحقق من وحدة التحكم'),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                }
                              },
                              icon: const Icon(Icons.bug_report),
                              label: const Text('اختبار قديم'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () async {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('جاري فحص جميع الملفات الصوتية...'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                                
                                final audioTestService = AudioTestService();
                                await audioTestService.testAllAudioFiles();
                                
                                if (context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('انتهى الفحص - تحقق من وحدة التحكم'),
                                      backgroundColor: Colors.blue,
                                    ),
                                  );
                                }
                              },
                              icon: const Icon(Icons.audio_file),
                              label: const Text('فحص الملفات'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildJuzButton(
    BuildContext context,
    ThemeService themeService, {
    required int juzNumber,
    required String title,
    required String subtitle,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      height: 100,
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SurahListScreen(juzNumber: juzNumber),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          elevation: 8,
          shadowColor: color.withOpacity(0.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Row(
          children: [
            const SizedBox(width: 20),
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(
                icon,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'QaloonFont',
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontFamily: 'QaloonFont',
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios_rounded,
              size: 30,
              color: Colors.white,
            ),
            const SizedBox(width: 20),
          ],
        ),
      ),
    );
  }
}
