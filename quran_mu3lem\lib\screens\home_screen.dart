import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter/foundation.dart';
import 'surah_list_screen.dart';
import '../utils/audio_test.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB), // Sky blue
              Color(0xFFE0F6FF), // Light blue
              Color(0xFFFFF8DC), // Cornsilk
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                // Header
                const SizedBox(height: 40),
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.9),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.menu_book_rounded,
                        size: 60,
                        color: Colors.green[700],
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'القرآن المعلم',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[800],
                          fontFamily: 'QaloonFont',
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        'الأجزاء الثلاثة الأخيرة',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.green[600],
                          fontFamily: 'QaloonFont',
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 60),
                
                // Juz buttons
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildJuzButton(
                        context,
                        juzNumber: 28,
                        title: 'الجزء الثامن والعشرون',
                        subtitle: 'قد سمع',
                        color: const Color(0xFF4CAF50),
                        icon: Icons.star_rounded,
                      ),
                      _buildJuzButton(
                        context,
                        juzNumber: 29,
                        title: 'الجزء التاسع والعشرون',
                        subtitle: 'تبارك',
                        color: const Color(0xFF2196F3),
                        icon: Icons.auto_awesome_rounded,
                      ),
                      _buildJuzButton(
                        context,
                        juzNumber: 30,
                        title: 'الجزء الثلاثون',
                        subtitle: 'عمّ',
                        color: const Color(0xFFFF9800),
                        icon: Icons.brightness_7_rounded,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Footer
                Text(
                  'برواية قالون عن نافع',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.green[700],
                    fontFamily: 'QaloonFont',
                  ),
                ),

                // Debug test button (only in debug mode)
                if (kDebugMode) ...[
                  const SizedBox(height: 20),
                  ElevatedButton.icon(
                    onPressed: () async {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('جاري تشغيل اختبارات الصوت...'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                      await AudioTest.runAllTests();
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('انتهت الاختبارات - تحقق من وحدة التحكم'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.bug_report),
                    label: const Text('اختبار الصوت'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildJuzButton(
    BuildContext context, {
    required int juzNumber,
    required String title,
    required String subtitle,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      width: double.infinity,
      height: 100,
      margin: const EdgeInsets.symmetric(vertical: 10),
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SurahListScreen(juzNumber: juzNumber),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          elevation: 8,
          shadowColor: color.withOpacity(0.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
        child: Row(
          children: [
            const SizedBox(width: 20),
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Icon(
                icon,
                size: 40,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'QaloonFont',
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontFamily: 'QaloonFont',
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios_rounded,
              size: 30,
              color: Colors.white,
            ),
            const SizedBox(width: 20),
          ],
        ),
      ),
    );
  }
}
