1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.quranmu3lem.app.quran_mu3lem"
4    android:versionCode="1001"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         Required to query activities that can process text, see:
12         https://developer.android.com/training/package-visibility and
13         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
14
15         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
16    -->
17    <queries>
17-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:39:5-44:15
18        <intent>
18-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:40:9-43:18
19            <action android:name="android.intent.action.PROCESS_TEXT" />
19-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:41:13-72
19-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:41:21-70
20
21            <data android:mimeType="text/plain" />
21-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:42:13-50
21-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:42:19-48
22        </intent>
23    </queries>
24
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784c75164cbdfbd8765b78e0df7f6470\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
25-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\784c75164cbdfbd8765b78e0df7f6470\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:22-76
26
27    <permission
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
28        android:name="com.quranmu3lem.app.quran_mu3lem.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.quranmu3lem.app.quran_mu3lem.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
32
33    <application
34        android:name="android.app.Application"
34-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:4:9-42
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
36        android:extractNativeLibs="true"
37        android:icon="@mipmap/ic_launcher"
37-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:5:9-43
38        android:label="quran_mu3lem" >
38-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:3:9-37
39        <activity
39-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:6:9-27:20
40            android:name="com.quranmu3lem.app.quran_mu3lem.MainActivity"
40-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:7:13-41
41            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
41-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:12:13-163
42            android:exported="true"
42-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:8:13-36
43            android:hardwareAccelerated="true"
43-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:13:13-47
44            android:launchMode="singleTop"
44-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:9:13-43
45            android:taskAffinity=""
45-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:10:13-36
46            android:theme="@style/LaunchTheme"
46-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:11:13-47
47            android:windowSoftInputMode="adjustResize" >
47-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:14:13-55
48
49            <!--
50                 Specifies an Android theme to apply to this Activity as soon as
51                 the Android process has started. This theme is visible to the user
52                 while the Flutter UI initializes. After that, this theme continues
53                 to determine the Window background behind the Flutter UI.
54            -->
55            <meta-data
55-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:19:13-22:17
56                android:name="io.flutter.embedding.android.NormalTheme"
56-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:20:15-70
57                android:resource="@style/NormalTheme" />
57-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:21:15-52
58
59            <intent-filter>
59-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:23:13-26:29
60                <action android:name="android.intent.action.MAIN" />
60-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:24:17-68
60-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:24:25-66
61
62                <category android:name="android.intent.category.LAUNCHER" />
62-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:25:17-76
62-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:25:27-74
63            </intent-filter>
64        </activity>
65        <!--
66             Don't delete the meta-data below.
67             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
68        -->
69        <meta-data
69-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:30:9-32:33
70            android:name="flutterEmbedding"
70-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:31:13-44
71            android:value="2" />
71-->D:\Project\QuranMu3lem\quran_mu3lem\android\app\src\main\AndroidManifest.xml:32:13-30
72
73        <uses-library
73-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
74            android:name="androidx.window.extensions"
74-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
75            android:required="false" />
75-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
76        <uses-library
76-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
77            android:name="androidx.window.sidecar"
77-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
78            android:required="false" />
78-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
79
80        <provider
80-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
81            android:name="androidx.startup.InitializationProvider"
81-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
82            android:authorities="com.quranmu3lem.app.quran_mu3lem.androidx-startup"
82-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
83            android:exported="false" >
83-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
84            <meta-data
84-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
85                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
85-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
86                android:value="androidx.startup" />
86-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
89                android:value="androidx.startup" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
90        </provider>
91
92        <receiver
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
93            android:name="androidx.profileinstaller.ProfileInstallReceiver"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
94            android:directBootAware="false"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
95            android:enabled="true"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
96            android:exported="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
97            android:permission="android.permission.DUMP" >
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
99                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
100            </intent-filter>
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
102                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
105                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
108                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
109            </intent-filter>
110        </receiver>
111    </application>
112
113</manifest>
