import 'package:json_annotation/json_annotation.dart';

part 'ayah.g.dart';

@JsonSerializable()
class Ayah {
  final int number;
  final String text;
  @JsonKey(name: 'surah_id')
  final int surahId;

  const Ayah({
    required this.number,
    required this.text,
    required this.surahId,
  });

  factory Ayah.fromJson(Map<String, dynamic> json) => _$AyahFromJson(json);
  Map<String, dynamic> toJson() => _$AyahToJson(this);

  String get displayText {
    return '$text ﴿$number﴾';
  }
}
