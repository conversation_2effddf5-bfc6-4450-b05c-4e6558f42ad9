// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Juz _$<PERSON>(Map<String, dynamic> json) => Juz(
  number: (json['number'] as num).toInt(),
  name: json['name'] as String,
  description: json['description'] as String,
  surahs:
      (json['surahs'] as List<dynamic>)
          .map((e) => Surah.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$<PERSON>(Juz instance) => <String, dynamic>{
  'number': instance.number,
  'name': instance.name,
  'description': instance.description,
  'surahs': instance.surahs,
};
