name: quran_mu3lem
description: "تطبيق تعليمي للأطفال لتعلم الأجزاء الثلاثة الأخيرة من القرآن الكريم برواية قالون"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Audio player for Quran recitation
  just_audio: ^0.9.39

  # Path provider for accessing local files
  path_provider: ^2.1.2

  # Shared preferences for settings
  shared_preferences: ^2.2.2

  # For JSON handling
  json_annotation: ^4.8.1

  # State management
  provider: ^6.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # JSON serialization code generation
  json_serializable: ^6.7.1
  build_runner: ^2.4.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets for the Quran app
  assets:
    - assets/data/
    - assets/data/quran_qaloon_complete.json
    - assets/audio/juz_26/
    - assets/audio/juz_27/
    - assets/audio/juz_28/
    - assets/audio/juz_29/
    - assets/audio/juz_30/
    - assets/audio/About.mp3
    - assets/images/

    # جميع الملفات الصوتية للجزء 26
    - assets/audio/juz_26/الأحقاف.mp3
    - assets/audio/juz_26/محمد.mp3
    - assets/audio/juz_26/الفتح.mp3
    - assets/audio/juz_26/الحجرات.mp3
    - assets/audio/juz_26/ق.mp3

    # جميع الملفات الصوتية للجزء 27
    - assets/audio/juz_27/الذاريات.mp3
    - assets/audio/juz_27/الطور.mp3
    - assets/audio/juz_27/النجم.mp3
    - assets/audio/juz_27/القمر.mp3
    - assets/audio/juz_27/الرحمن.mp3
    - assets/audio/juz_27/الواقعة.mp3
    - assets/audio/juz_27/الحديد.mp3

    # جميع الملفات الصوتية للجزء 28 (قد سمع)
    - assets/audio/juz_28/المجادلة.mp3
    - assets/audio/juz_28/الحشر.mp3
    - assets/audio/juz_28/الممتحنة.mp3
    - assets/audio/juz_28/الصف.mp3
    - assets/audio/juz_28/الجمعة.mp3
    - assets/audio/juz_28/المنافقون.mp3
    - assets/audio/juz_28/التغابن.mp3
    - assets/audio/juz_28/الطلاق.mp3
    - assets/audio/juz_28/التحريم.mp3
    # جميع الملفات الصوتية للجزء 29
    - assets/audio/juz_29/الملك.mp3
    - assets/audio/juz_29/القلم.mp3
    - assets/audio/juz_29/الحاقة.mp3
    - assets/audio/juz_29/المعارج.mp3
    - assets/audio/juz_29/نوح.mp3
    - assets/audio/juz_29/الجن.mp3
    - assets/audio/juz_29/المزمل.mp3
    - assets/audio/juz_29/المدثر.mp3
    - assets/audio/juz_29/القيامة.mp3
    - assets/audio/juz_29/الانسان.mp3
    - assets/audio/juz_29/المرسلات.mp3
    # جميع الملفات الصوتية للجزء 30
    - assets/audio/juz_30/الفاتحة.mp3
    - assets/audio/juz_30/النبأ.mp3
    - assets/audio/juz_30/النازعات.mp3
    - assets/audio/juz_30/عبس.mp3
    - assets/audio/juz_30/التكوير.mp3
    - assets/audio/juz_30/الانفطار.mp3
    - assets/audio/juz_30/المطففين.mp3
    - assets/audio/juz_30/الانشقاق.mp3
    - assets/audio/juz_30/البروج.mp3
    - assets/audio/juz_30/الطارق.mp3
    - assets/audio/juz_30/الاعلى.mp3
    - assets/audio/juz_30/الغاشية.mp3
    - assets/audio/juz_30/الفجر.mp3
    - assets/audio/juz_30/البلد.mp3
    - assets/audio/juz_30/الشمس.mp3
    - assets/audio/juz_30/الليل.mp3
    - assets/audio/juz_30/الضحى.mp3
    - assets/audio/juz_30/الشرح.mp3
    - assets/audio/juz_30/التين.mp3
    - assets/audio/juz_30/العلق.mp3
    - assets/audio/juz_30/القدر.mp3
    - assets/audio/juz_30/البينة.mp3
    - assets/audio/juz_30/الزلزلة.mp3
    - assets/audio/juz_30/العاديات.mp3
    - assets/audio/juz_30/القارعة.mp3
    - assets/audio/juz_30/التكاثر.mp3
    - assets/audio/juz_30/العصر.mp3
    - assets/audio/juz_30/الهمزة.mp3
    - assets/audio/juz_30/الفيل.mp3
    - assets/audio/juz_30/قريش.mp3
    - assets/audio/juz_30/الماعون.mp3
    - assets/audio/juz_30/الكوثر.mp3
    - assets/audio/juz_30/الكافرون.mp3
    - assets/audio/juz_30/النصر.mp3
    - assets/audio/juz_30/المسد.mp3
    - assets/audio/juz_30/الخلاص.mp3
    - assets/audio/juz_30/الفلق.mp3
    - assets/audio/juz_30/الناس.mp3

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # Custom fonts for the Quran app
  fonts:
    - family: QaloonFont
      fonts:
        - asset: assets/fonts/Qaloonfont.otf
