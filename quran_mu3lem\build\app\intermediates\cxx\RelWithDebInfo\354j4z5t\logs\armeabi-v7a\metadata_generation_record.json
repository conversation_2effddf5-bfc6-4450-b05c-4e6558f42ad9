[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: armeabi-v7a", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\Project\\QuranMu3lem\\quran_mu3lem\\android\\app\\.cxx\\RelWithDebInfo\\354j4z5t\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "tag_": "release|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]