import 'package:just_audio/just_audio.dart';
import 'package:flutter/foundation.dart';

class AboutAudioService {
  static final AboutAudioService _instance = AboutAudioService._internal();
  factory AboutAudioService() => _instance;
  AboutAudioService._internal();

  AudioPlayer? _audioPlayer;
  bool _isPlaying = false;
  bool _isLoading = false;

  bool get isPlaying => _isPlaying;
  bool get isLoading => _isLoading;

  /// تشغيل ملف "عن التطبيق"
  Future<void> playAboutAudio() async {
    try {
      if (_isLoading) return;

      _isLoading = true;
      
      if (kDebugMode) {
        print('🎵 بدء تحميل ملف عن التطبيق...');
      }

      // إيقاف أي تشغيل سابق
      await stopAudio();

      // إنشاء مشغل جديد
      _audioPlayer = AudioPlayer();

      // تحميل الملف
      await _audioPlayer!.setAsset('assets/audio/About.mp3');

      if (kDebugMode) {
        print('✅ تم تحميل ملف عن التطبيق بنجاح');
      }

      // بدء التشغيل
      await _audioPlayer!.play();
      _isPlaying = true;

      // الاستماع لانتهاء التشغيل
      _audioPlayer!.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          _isPlaying = false;
          if (kDebugMode) {
            print('🏁 انتهى تشغيل ملف عن التطبيق');
          }
        }
      });

      if (kDebugMode) {
        print('🎵 بدأ تشغيل ملف عن التطبيق');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تشغيل ملف عن التطبيق: $e');
      }
      _isPlaying = false;
    } finally {
      _isLoading = false;
    }
  }

  /// إيقاف التشغيل
  Future<void> stopAudio() async {
    try {
      if (_audioPlayer != null) {
        await _audioPlayer!.stop();
        await _audioPlayer!.dispose();
        _audioPlayer = null;
      }
      _isPlaying = false;
      
      if (kDebugMode) {
        print('⏹️ تم إيقاف تشغيل ملف عن التطبيق');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إيقاف ملف عن التطبيق: $e');
      }
    }
  }

  /// تبديل التشغيل/الإيقاف
  Future<void> togglePlayback() async {
    if (_isPlaying) {
      await stopAudio();
    } else {
      await playAboutAudio();
    }
  }

  /// تنظيف الموارد
  Future<void> dispose() async {
    await stopAudio();
  }
}
