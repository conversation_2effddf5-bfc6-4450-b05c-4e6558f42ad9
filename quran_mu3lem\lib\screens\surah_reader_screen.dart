import 'package:flutter/material.dart';
import '../models/surah.dart';
import '../services/audio_service.dart';
import '../widgets/audio_controls.dart';

class SurahReaderScreen extends StatefulWidget {
  final Surah surah;

  const SurahReaderScreen({super.key, required this.surah});

  @override
  State<SurahReaderScreen> createState() => _SurahReaderScreenState();
}

class _SurahReaderScreenState extends State<SurahReaderScreen> {
  final AudioService _audioService = AudioService();
  bool _isDarkMode = false;

  @override
  void initState() {
    super.initState();
    _initializeAudio();
  }

  Future<void> _initializeAudio() async {
    try {
      await _audioService.playSurah(widget.surah);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الصوت: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () {
                _initializeAudio();
              },
            ),
          ),
        );
      }
    }
  }

  Color get _primaryColor {
    switch (widget.surah.juzNumber) {
      case 28:
        return const Color(0xFF4CAF50);
      case 29:
        return const Color(0xFF2196F3);
      case 30:
        return const Color(0xFFFF9800);
      default:
        return const Color(0xFF4CAF50);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _isDarkMode ? const Color(0xFF2D2D2D) : _primaryColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () {
                      _audioService.stop();
                      Navigator.pop(context);
                    },
                    icon: Icon(
                      Icons.arrow_back_ios_rounded,
                      color: _isDarkMode ? Colors.white : _primaryColor,
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          widget.surah.name,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: _isDarkMode ? Colors.white : _primaryColor,
                            fontFamily: 'QaloonFont',
                          ),
                        ),
                        Text(
                          '${widget.surah.ayahCount} آية - ${widget.surah.juzName}',
                          style: TextStyle(
                            fontSize: 14,
                            color: _isDarkMode ? Colors.grey[400] : _primaryColor.withOpacity(0.7),
                            fontFamily: 'QaloonFont',
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _isDarkMode = !_isDarkMode;
                      });
                    },
                    icon: Icon(
                      _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                      color: _isDarkMode ? Colors.white : _primaryColor,
                    ),
                  ),
                ],
              ),
            ),

            // Surah content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Bismillah (except for Al-Fatiha and At-Tawbah)
                    if (widget.surah.id != 1 && widget.surah.id != 9)
                      Container(
                        padding: const EdgeInsets.all(20),
                        margin: const EdgeInsets.only(bottom: 30),
                        decoration: BoxDecoration(
                          color: _isDarkMode ? const Color(0xFF2D2D2D) : _primaryColor.withOpacity(0.05),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: _isDarkMode ? Colors.grey[700]! : _primaryColor.withOpacity(0.2),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                            style: TextStyle(
                              fontSize: 24,
                              fontFamily: 'QaloonFont',
                              color: _isDarkMode ? Colors.white : Colors.black87,
                              height: 2.0,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),

                    // Placeholder for Surah text
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: _isDarkMode ? const Color(0xFF2D2D2D) : Colors.grey[50],
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color: _isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.menu_book_rounded,
                            size: 64,
                            color: _isDarkMode ? Colors.grey[600] : Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'نص السورة',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: _isDarkMode ? Colors.white : Colors.black87,
                              fontFamily: 'QaloonFont',
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'سيتم إضافة نص السورة هنا من ملف النص القرآني',
                            style: TextStyle(
                              fontSize: 16,
                              color: _isDarkMode ? Colors.grey[400] : Colors.grey[600],
                              fontFamily: 'QaloonFont',
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 20),
                          Text(
                            'يمكنك الاستماع للسورة باستخدام أزرار التحكم أدناه',
                            style: TextStyle(
                              fontSize: 14,
                              color: _isDarkMode ? Colors.grey[500] : Colors.grey[500],
                              fontFamily: 'QaloonFont',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Audio controls
            AudioControls(
              audioService: _audioService,
              primaryColor: _primaryColor,
              isDarkMode: _isDarkMode,
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _audioService.stop();
    super.dispose();
  }
}
