import 'package:flutter/material.dart';
import '../models/surah.dart';
import '../models/ayah.dart';
import '../services/audio_service.dart';
import '../services/quran_text_service.dart';
import '../widgets/audio_controls.dart';

class SurahReaderScreen extends StatefulWidget {
  final Surah surah;

  const SurahReaderScreen({super.key, required this.surah});

  @override
  State<SurahReaderScreen> createState() => _SurahReaderScreenState();
}

class _SurahReaderScreenState extends State<SurahReaderScreen> {
  final AudioService _audioService = AudioService();
  final QuranTextService _textService = QuranTextService();
  bool _isDarkMode = false;
  List<Ayah> _ayahs = [];
  bool _isLoadingText = true;
  String _textError = '';

  @override
  void initState() {
    super.initState();
    // لا نحمل الصوت تلقائياً، بل ننتظر حتى يضغط المستخدم على زر التشغيل
    _loadSurahText();
  }

  Future<void> _loadSurahText() async {
    try {
      setState(() {
        _isLoadingText = true;
        _textError = '';
      });

      final ayahs = await _textService.getAyahsBySurah(widget.surah.id);

      setState(() {
        _ayahs = ayahs;
        _isLoadingText = false;
      });
    } catch (e) {
      setState(() {
        _textError = e.toString();
        _isLoadingText = false;
      });
    }
  }

  Future<void> _initializeAudio() async {
    if (_audioService.isLoading) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('جاري تحميل ملف صوتي آخر، يرجى الانتظار...'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 2),
          ),
        );
      }
      return;
    }

    try {
      // عرض رسالة تحميل
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 16),
                Text('جاري تحميل ${widget.surah.name}...'),
              ],
            ),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      await _audioService.playSurah(widget.surah);

      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('تم تحميل ${widget.surah.name} بنجاح'),
                      Text(
                        'الحالة: ${_audioService.playerStatus}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.error, color: Colors.white),
                    const SizedBox(width: 8),
                    const Text('فشل تحميل الصوت'),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  e.toString(),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 8),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () {
                _initializeAudio();
              },
            ),
          ),
        );
      }
    }
  }

  Color get _primaryColor {
    switch (widget.surah.juzNumber) {
      case 28:
        return const Color(0xFF4CAF50);
      case 29:
        return const Color(0xFF2196F3);
      case 30:
        return const Color(0xFFFF9800);
      default:
        return const Color(0xFF4CAF50);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _isDarkMode ? const Color(0xFF2D2D2D) : _primaryColor.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () {
                      _audioService.stop();
                      Navigator.pop(context);
                    },
                    icon: Icon(
                      Icons.arrow_back_ios_rounded,
                      color: _isDarkMode ? Colors.white : _primaryColor,
                    ),
                  ),
                  Expanded(
                    child: Column(
                      children: [
                        Text(
                          widget.surah.name,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: _isDarkMode ? Colors.white : _primaryColor,
                            fontFamily: 'QaloonFont',
                          ),
                        ),
                        Text(
                          '${widget.surah.ayahCount} آية - ${widget.surah.juzName}',
                          style: TextStyle(
                            fontSize: 14,
                            color: _isDarkMode ? Colors.grey[400] : _primaryColor.withOpacity(0.7),
                            fontFamily: 'QaloonFont',
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _isDarkMode = !_isDarkMode;
                      });
                    },
                    icon: Icon(
                      _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                      color: _isDarkMode ? Colors.white : _primaryColor,
                    ),
                  ),
                ],
              ),
            ),

            // Surah content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Bismillah (except for Al-Fatiha and At-Tawbah)
                    if (widget.surah.id != 1 && widget.surah.id != 9)
                      Container(
                        padding: const EdgeInsets.all(20),
                        margin: const EdgeInsets.only(bottom: 30),
                        decoration: BoxDecoration(
                          color: _isDarkMode ? const Color(0xFF2D2D2D) : _primaryColor.withOpacity(0.05),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: _isDarkMode ? Colors.grey[700]! : _primaryColor.withOpacity(0.2),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                            style: TextStyle(
                              fontSize: 24,
                              fontFamily: 'QaloonFont',
                              color: _isDarkMode ? Colors.white : Colors.black87,
                              height: 2.0,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),

                    // Surah text content
                    _buildSurahTextContent(),
                  ],
                ),
              ),
            ),

            // Audio controls
            AudioControls(
              audioService: _audioService,
              primaryColor: _primaryColor,
              isDarkMode: _isDarkMode,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSurahTextContent() {
    if (_isLoadingText) {
      return Container(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            CircularProgressIndicator(
              color: _primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'جاري تحميل نص السورة...',
              style: TextStyle(
                fontSize: 16,
                color: _isDarkMode ? Colors.grey[400] : Colors.grey[600],
                fontFamily: 'QaloonFont',
              ),
            ),
          ],
        ),
      );
    }

    if (_textError.isNotEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[600],
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل النص',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
                fontFamily: 'QaloonFont',
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _textError,
              style: TextStyle(
                fontSize: 14,
                color: Colors.red[600],
                fontFamily: 'QaloonFont',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadSurahText,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    if (_ayahs.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: Text(
          'لا توجد آيات متاحة لهذه السورة',
          style: TextStyle(
            fontSize: 16,
            color: _isDarkMode ? Colors.grey[400] : Colors.grey[600],
            fontFamily: 'QaloonFont',
          ),
          textAlign: TextAlign.center,
        ),
      );
    }

    return Column(
      children: [
        // Audio control button
        Container(
          margin: const EdgeInsets.only(bottom: 20),
          child: ElevatedButton.icon(
            onPressed: () {
              _initializeAudio();
            },
            icon: const Icon(Icons.download_rounded),
            label: const Text(
              'تحميل الصوت',
              style: TextStyle(fontFamily: 'QaloonFont'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: _primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ),

        // Surah text
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: _isDarkMode ? const Color(0xFF2D2D2D) : Colors.grey[50],
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: _isDarkMode ? Colors.grey[700]! : Colors.grey[200]!,
            ),
          ),
          child: Column(
            children: [
              // Surah header
              Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  widget.surah.name,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: _isDarkMode ? Colors.white : Colors.black87,
                    fontFamily: 'QaloonFont',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const Divider(),

              const SizedBox(height: 16),

              // Ayahs
              ...(_ayahs.map((ayah) => _buildAyahWidget(ayah)).toList()),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAyahWidget(Ayah ayah) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Ayah text
          Text(
            ayah.text,
            style: TextStyle(
              fontSize: 24,
              height: 2.0,
              color: _isDarkMode ? Colors.white : Colors.black87,
              fontFamily: 'QaloonFont',
            ),
            textAlign: TextAlign.justify,
            textDirection: TextDirection.rtl,
          ),

          const SizedBox(height: 12),

          // Ayah number
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: _primaryColor.withOpacity(0.3)),
                ),
                child: Text(
                  '﴿${_convertToArabicNumbers(ayah.number)}﴾',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                    fontFamily: 'QaloonFont',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _convertToArabicNumbers(int number) {
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitIndex = int.tryParse(digit);
      return digitIndex != null ? arabicDigits[digitIndex] : digit;
    }).join();
  }

  @override
  void dispose() {
    _audioService.stop();
    super.dispose();
  }
}
