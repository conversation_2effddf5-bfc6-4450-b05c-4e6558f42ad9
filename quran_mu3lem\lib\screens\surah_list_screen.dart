import 'package:flutter/material.dart';
import '../models/surah.dart';
import '../services/quran_data_service.dart';
import 'surah_reader_screen.dart';

class SurahListScreen extends StatefulWidget {
  final int juzNumber;

  const SurahListScreen({super.key, required this.juzNumber});

  @override
  State<SurahListScreen> createState() => _SurahListScreenState();
}

class _SurahListScreenState extends State<SurahListScreen> {
  final QuranDataService _dataService = QuranDataService();
  List<Surah> _surahs = [];
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadSurahs();
  }

  Future<void> _loadSurahs() async {
    try {
      final surahs = await _dataService.getSurahsByJuz(widget.juzNumber);
      setState(() {
        _surahs = surahs;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  String get _juzName {
    switch (widget.juzNumber) {
      case 28:
        return 'قد سمع';
      case 29:
        return 'تبارك';
      case 30:
        return 'عمّ';
      default:
        return 'الجزء ${widget.juzNumber}';
    }
  }

  Color get _juzColor {
    switch (widget.juzNumber) {
      case 28:
        return const Color(0xFF4CAF50);
      case 29:
        return const Color(0xFF2196F3);
      case 30:
        return const Color(0xFFFF9800);
      default:
        return const Color(0xFF4CAF50);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              _juzColor.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(
                        Icons.arrow_back_ios_rounded,
                        color: _juzColor,
                        size: 28,
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                            _juzName,
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: _juzColor,
                              fontFamily: 'QaloonFont',
                            ),
                          ),
                          Text(
                            'الجزء ${widget.juzNumber}',
                            style: TextStyle(
                              fontSize: 16,
                              color: _juzColor.withOpacity(0.7),
                              fontFamily: 'QaloonFont',
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 48), // Balance the back button
                  ],
                ),
              ),
              
              // Content
              Expanded(
                child: _isLoading
                    ? Center(
                        child: CircularProgressIndicator(
                          color: _juzColor,
                        ),
                      )
                    : _errorMessage.isNotEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 64,
                                  color: Colors.red[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'خطأ في تحميل البيانات',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Colors.red[600],
                                    fontFamily: 'QaloonFont',
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _errorMessage,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _loadSurahs,
                                  child: const Text('إعادة المحاولة'),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _surahs.length,
                            itemBuilder: (context, index) {
                              final surah = _surahs[index];
                              return _buildSurahCard(surah, index);
                            },
                          ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSurahCard(Surah surah, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SurahReaderScreen(surah: surah),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Surah number circle
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: _juzColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${surah.orderNumber}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Surah info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        surah.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'QaloonFont',
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${surah.ayahCount} آية',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontFamily: 'QaloonFont',
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Play icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _juzColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.play_arrow_rounded,
                    color: _juzColor,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
