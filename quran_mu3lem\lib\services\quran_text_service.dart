import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import '../models/ayah.dart';

class QuranTextService {
  static final QuranTextService _instance = QuranTextService._internal();
  factory QuranTextService() => _instance;
  QuranTextService._internal();

  Map<int, List<Ayah>>? _cachedAyahs;
  Map<String, dynamic>? _quranData;

  /// تحميل بيانات القرآن الكاملة
  Future<void> _loadQuranData() async {
    if (_quranData != null) return;

    try {
      final String jsonString = await rootBundle.loadString('assets/data/quran_qaloon_complete.json');
      _quranData = json.decode(jsonString);
      
      if (kDebugMode) {
        print('✅ تم تحميل بيانات القرآن الكاملة');
        print('📊 عدد السور: ${_quranData!['surahs'].length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحميل بيانات القرآن: $e');
      }
      throw Exception('فشل في تحميل نصوص القرآن: $e');
    }
  }

  /// الحصول على آيات سورة معينة
  Future<List<Ayah>> getAyahsBySurah(int surahId) async {
    // التحقق من الذاكرة المؤقتة أولاً
    if (_cachedAyahs != null && _cachedAyahs!.containsKey(surahId)) {
      return _cachedAyahs![surahId]!;
    }

    await _loadQuranData();

    try {
      // البحث عن السورة في البيانات
      final List<dynamic> surahs = _quranData!['surahs'];
      final surahData = surahs.firstWhere(
        (surah) => surah['id'] == surahId,
        orElse: () => null,
      );

      if (surahData == null) {
        throw Exception('السورة رقم $surahId غير موجودة');
      }

      // استخراج الآيات
      final List<dynamic> ayahsData = surahData['ayahs'];
      final List<Ayah> ayahs = ayahsData.map((ayahData) {
        return Ayah(
          number: ayahData['ayah_number'],
          text: ayahData['text'],
          surahId: surahId,
        );
      }).toList();

      // حفظ في الذاكرة المؤقتة
      _cachedAyahs ??= {};
      _cachedAyahs![surahId] = ayahs;

      if (kDebugMode) {
        print('✅ تم تحميل ${ayahs.length} آية للسورة رقم $surahId');
      }

      return ayahs;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في استخراج آيات السورة $surahId: $e');
      }
      throw Exception('فشل في تحميل آيات السورة: $e');
    }
  }

  /// الحصول على نص السورة كاملاً
  Future<String> getSurahFullText(int surahId) async {
    final ayahs = await getAyahsBySurah(surahId);
    
    // دمج جميع الآيات في نص واحد
    final StringBuffer fullText = StringBuffer();
    
    for (int i = 0; i < ayahs.length; i++) {
      final ayah = ayahs[i];
      
      // إضافة نص الآية
      fullText.write(ayah.text);
      
      // إضافة رقم الآية بالتنسيق العربي
      fullText.write(' ﴿${_convertToArabicNumbers(ayah.number)}﴾');
      
      // إضافة مسافة بين الآيات (ما عدا الآية الأخيرة)
      if (i < ayahs.length - 1) {
        fullText.write(' ');
      }
    }
    
    return fullText.toString();
  }

  /// الحصول على آيات متعددة السور (للأجزاء)
  Future<Map<int, List<Ayah>>> getAyahsForMultipleSurahs(List<int> surahIds) async {
    final Map<int, List<Ayah>> result = {};
    
    for (final surahId in surahIds) {
      try {
        result[surahId] = await getAyahsBySurah(surahId);
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ تخطي السورة $surahId بسبب خطأ: $e');
        }
      }
    }
    
    return result;
  }

  /// الحصول على معلومات السورة
  Future<Map<String, dynamic>?> getSurahInfo(int surahId) async {
    await _loadQuranData();

    try {
      final List<dynamic> surahs = _quranData!['surahs'];
      final surahData = surahs.firstWhere(
        (surah) => surah['id'] == surahId,
        orElse: () => null,
      );

      if (surahData == null) return null;

      return {
        'id': surahData['id'],
        'name': surahData['name'],
        'ayah_count': surahData['ayah_count'],
        'order_number': surahData['order_number'],
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على معلومات السورة $surahId: $e');
      }
      return null;
    }
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  String _convertToArabicNumbers(int number) {
    const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().split('').map((digit) {
      final digitIndex = int.tryParse(digit);
      return digitIndex != null ? arabicDigits[digitIndex] : digit;
    }).join();
  }

  /// مسح الذاكرة المؤقتة
  void clearCache() {
    _cachedAyahs?.clear();
    _quranData = null;
  }

  /// الحصول على إحصائيات الذاكرة المؤقتة
  Map<String, int> getCacheStats() {
    return {
      'cached_surahs': _cachedAyahs?.length ?? 0,
      'total_cached_ayahs': _cachedAyahs?.values
          .fold<int>(0, (sum, ayahs) => sum + ayahs.length) ?? 0,
    };
  }
}
