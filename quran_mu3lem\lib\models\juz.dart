import 'package:json_annotation/json_annotation.dart';
import 'surah.dart';

part 'juz.g.dart';

@JsonSerializable()
class Juz {
  final int number;
  final String name;
  final String description;
  final List<Surah> surahs;

  const <PERSON><PERSON>({
    required this.number,
    required this.name,
    required this.description,
    required this.surahs,
  });

  factory Juz.fromJson(Map<String, dynamic> json) => _$JuzFromJson(json);
  Map<String, dynamic> toJson() => _$JuzToJson(this);

  String get displayName {
    return 'الجزء $number - $name';
  }

  int get totalAyahs {
    return surahs.fold(0, (sum, surah) => sum + surah.ayahCount);
  }
}
