 Prompt لتطبيق موبايل تعليمي لأجزاء القرآن الثلاثة الأخيرة
الهدف:
إنشاء تطبيق موبايل تعليمي للأطفال، يعرض الأجزاء الثلاثة الأخيرة من القرآن الكريم (الجزء 30، 29، 28) برواية قالون عن نافع، مع إمكانية الاستماع للصوت عند اختيار السورة وظهور الآيات بالتزامن.

المتطلبات:

المنصة: Android و iOS (باستخدام Flutter).

الواجهة: بسيطة، جذابة، برسومات طفولية، تدعم اللغة العربية بالكامل.

الوظائف الأساسية:

شاشة رئيسية جميلة وجذابة وبالوان طفولية تحتوي على قائمة بثلاثة أزرار تمثل:

الجزء 28

الجزء 29

الجزء 30

عند الضغط على أي جزء، تُفتح شاشة جديدة بها قائمة بجميع السور الموجودة في ذلك الجزء.

عند اختيار سورة:

يتم تشغيل الصوت الخاص بالسورة (من ملفات الصوت الجاهزة).

تظهر الآيات مكتوبة بخط الموجود في المسار D:\Project\QuranMu3lem باسم Qaloonfont.otf برواية قالون، .

زر إيقاف/تشغيل الصوت.

دعم التكرار التلقائي.

دعم الانتقال التلقائي للآية التالية.

البيانات:

ملف نصي للقرآن الكريم برواية قالون يتم استخدامه لاستخراج النصوص بحسب السور من ملف النص الموجود في هذا المسار D:\Project\QuranMu3lem باسم Qaloontxt.docx .

ملفات صوتية مرتبة حسب السور من ملفات الصوت الجاهزةوالمرتبة حسب الجزء الموجود بهذا المسار D:\Project\QuranMu3lem (بصيغة mp3 ).

ملف JSON موجود في هذا المسار D:\Project\QuranMu3lem باسم surahs_index.json يحتوي على كل سورة مع:

اسمها

ترتيبها

الآيات


خيارات إضافية:

تفعيل الوضع الليلي.

واجهة تفاعلية للطفل (رموز، ألوان جذابة، خطوط كبيرة).


دعم التصفح الأفقي أو الرأسي للنصوص.

اللغة: اللغة العربية فقط.

أسماء الأجزاء داخل التطبيق:

الجزء 28: قد سمع

الجزء 29: تبارك

الجزء 30: عمّ
قم بإضافة سورة الفاتحة الى جزء عمّ

التقنيات المقترحة:

Flutter (للتوافق الكامل بين iOS و Android)

استخدام audioplayers أو just_audio لتشغيل الصوت

قاعدة بيانات محلية لربط السور بالآيات والصوت والنصوص

